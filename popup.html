<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .sort-option {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .sort-option:hover {
      background-color: #f5f5f5;
    }
    .sort-option.active {
      background-color: #ff0050;
      color: white;
      border-color: #ff0050;
    }
    .sort-button {
      width: 100%;
      padding: 12px;
      background-color: #ff0050;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 15px;
    }
    .sort-button:hover {
      background-color: #e6004a;
    }
    .reset-button {
      width: 100%;
      padding: 10px;
      background-color: #666;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      margin-top: 10px;
    }
    .reset-button:hover {
      background-color: #555;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>TikTok搜索排序</h3>
  </div>
  
  <div class="sort-option" data-sort="likes-desc">
    📈 按点赞量排序 (高到低)
  </div>
  
  <div class="sort-option" data-sort="likes-asc">
    📉 按点赞量排序 (低到高)
  </div>
  
  <div class="sort-option" data-sort="time-desc">
    🕐 按时间排序 (最新)
  </div>
  
  <div class="sort-option" data-sort="time-asc">
    🕑 按时间排序 (最旧)
  </div>
  
  <button class="sort-button" id="applySortBtn">应用排序</button>
  <button class="reset-button" id="resetBtn">恢复原始顺序</button>
  
  <script src="popup.js"></script>
</body>
</html>
