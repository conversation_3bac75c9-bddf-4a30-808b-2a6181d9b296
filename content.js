// TikTok搜索结果排序器
class TikTokSorter {
  constructor() {
    this.originalOrder = [];
    this.isInitialized = false;
    this.init();
  }
  
  init() {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }
  
  setup() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'sort') {
        this.sortVideos(request.sortType);
        sendResponse({success: true});
      } else if (request.action === 'reset') {
        this.resetOrder();
        sendResponse({success: true});
      }
    });
    
    // 保存原始顺序
    this.saveOriginalOrder();
  }
  
  // 获取视频容器
  getVideoContainers() {
    // TikTok搜索结果的视频容器选择器
    const selectors = [
      '[data-e2e="search_top-item"]',
      '[data-e2e="search-card-item"]',
      'div[data-e2e*="search"]',
      '.tiktok-search-item',
      // 通用选择器，根据TikTok的DOM结构
      'div[class*="DivItemContainer"]',
      'div[class*="ItemContainer"]'
    ];
    
    let containers = [];
    for (const selector of selectors) {
      containers = document.querySelectorAll(selector);
      if (containers.length > 0) {
        console.log(`找到 ${containers.length} 个视频容器，使用选择器: ${selector}`);
        break;
      }
    }
    
    // 如果没有找到，尝试更通用的方法
    if (containers.length === 0) {
      // 查找包含视频的div
      const allDivs = document.querySelectorAll('div');
      containers = Array.from(allDivs).filter(div => {
        const hasVideo = div.querySelector('video') || div.querySelector('[data-e2e*="video"]');
        const hasLikes = div.textContent.includes('❤') || div.querySelector('[data-e2e*="like"]');
        return hasVideo && hasLikes;
      });
      console.log(`通过通用方法找到 ${containers.length} 个视频容器`);
    }
    
    return Array.from(containers);
  }
  
  // 保存原始顺序
  saveOriginalOrder() {
    const containers = this.getVideoContainers();
    if (containers.length > 0) {
      this.originalOrder = containers.map(container => ({
        element: container,
        parent: container.parentNode,
        nextSibling: container.nextSibling
      }));
      this.isInitialized = true;
      console.log(`保存了 ${this.originalOrder.length} 个视频的原始顺序`);
    }
  }
  
  // 提取点赞数
  extractLikes(container) {
    const likeSelectors = [
      '[data-e2e="like-count"]',
      '[data-e2e*="like"]',
      '.like-count',
      'span[class*="like"]',
      'div[class*="like"]'
    ];
    
    let likesText = '';
    for (const selector of likeSelectors) {
      const element = container.querySelector(selector);
      if (element) {
        likesText = element.textContent.trim();
        break;
      }
    }
    
    // 如果没找到专门的点赞元素，搜索包含❤或数字+K/M的文本
    if (!likesText) {
      const textElements = container.querySelectorAll('span, div, p');
      for (const element of textElements) {
        const text = element.textContent.trim();
        if (text.includes('❤') || /\d+[KM]?$/.test(text)) {
          likesText = text;
          break;
        }
      }
    }
    
    // 转换为数字
    return this.parseNumber(likesText);
  }
  
  // 提取发布时间
  extractTime(container) {
    const timeSelectors = [
      '[data-e2e="video-time"]',
      'time',
      '.time',
      'span[class*="time"]',
      'div[class*="time"]'
    ];
    
    let timeText = '';
    for (const selector of timeSelectors) {
      const element = container.querySelector(selector);
      if (element) {
        timeText = element.textContent.trim();
        break;
      }
    }
    
    // 如果没找到，搜索时间相关的文本
    if (!timeText) {
      const textElements = container.querySelectorAll('span, div, p');
      for (const element of textElements) {
        const text = element.textContent.trim();
        if (this.isTimeText(text)) {
          timeText = text;
          break;
        }
      }
    }
    
    return this.parseTime(timeText);
  }
  
  // 判断是否为时间文本
  isTimeText(text) {
    const timePatterns = [
      /\d+[分时天周月年]/,
      /\d+\s*(min|hour|day|week|month|year)s?\s*ago/i,
      /\d+-\d+-\d+/,
      /\d+:\d+/
    ];
    
    return timePatterns.some(pattern => pattern.test(text));
  }
  
  // 解析数字（处理K, M等单位）
  parseNumber(text) {
    if (!text) return 0;
    
    const cleanText = text.replace(/[^\d.KM]/gi, '');
    const number = parseFloat(cleanText);
    
    if (isNaN(number)) return 0;
    
    if (cleanText.toUpperCase().includes('K')) {
      return number * 1000;
    } else if (cleanText.toUpperCase().includes('M')) {
      return number * 1000000;
    }
    
    return number;
  }
  
  // 解析时间为时间戳
  parseTime(timeText) {
    if (!timeText) return 0;
    
    const now = Date.now();
    const text = timeText.toLowerCase();
    
    // 处理相对时间
    if (text.includes('分') || text.includes('min')) {
      const minutes = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (minutes * 60 * 1000);
    } else if (text.includes('时') || text.includes('hour')) {
      const hours = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (hours * 60 * 60 * 1000);
    } else if (text.includes('天') || text.includes('day')) {
      const days = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (days * 24 * 60 * 60 * 1000);
    } else if (text.includes('周') || text.includes('week')) {
      const weeks = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (weeks * 7 * 24 * 60 * 60 * 1000);
    } else if (text.includes('月') || text.includes('month')) {
      const months = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (months * 30 * 24 * 60 * 60 * 1000);
    } else if (text.includes('年') || text.includes('year')) {
      const years = parseInt(text.match(/\d+/)?.[0] || '0');
      return now - (years * 365 * 24 * 60 * 60 * 1000);
    }
    
    // 处理绝对时间格式
    const dateMatch = timeText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (dateMatch) {
      return new Date(dateMatch[0]).getTime();
    }
    
    return now; // 默认返回当前时间
  }
  
  // 排序视频
  sortVideos(sortType) {
    if (!this.isInitialized) {
      this.saveOriginalOrder();
    }

    if (this.originalOrder.length === 0) {
      console.log('没有找到视频容器');
      this.showNotification('未找到视频内容，请确保在TikTok搜索页面');
      return;
    }

    const containers = this.originalOrder.map(item => item.element);
    const parent = this.originalOrder[0].parent;

    // 提取数据并排序
    const videoData = containers.map(container => ({
      element: container,
      likes: this.extractLikes(container),
      time: this.extractTime(container)
    }));

    console.log('视频数据:', videoData.map(v => ({likes: v.likes, time: new Date(v.time)})));

    // 根据排序类型排序
    let sortName = '';
    switch (sortType) {
      case 'likes-desc':
        videoData.sort((a, b) => b.likes - a.likes);
        sortName = '按点赞量（高到低）';
        break;
      case 'likes-asc':
        videoData.sort((a, b) => a.likes - b.likes);
        sortName = '按点赞量（低到高）';
        break;
      case 'time-desc':
        videoData.sort((a, b) => b.time - a.time);
        sortName = '按时间（最新）';
        break;
      case 'time-asc':
        videoData.sort((a, b) => a.time - b.time);
        sortName = '按时间（最旧）';
        break;
    }

    // 重新排列DOM元素
    videoData.forEach(item => {
      parent.appendChild(item.element);
    });

    this.showNotification(`排序完成：${sortName}`);
    console.log(`按 ${sortType} 排序完成`);
  }

  // 显示通知
  showNotification(message) {
    // 移除现有通知
    const existingNotification = document.querySelector('.tiktok-sorter-indicator');
    if (existingNotification) {
      existingNotification.remove();
    }

    // 创建新通知
    const notification = document.createElement('div');
    notification.className = 'tiktok-sorter-indicator';
    notification.textContent = message;
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }
  
  // 恢复原始顺序
  resetOrder() {
    if (this.originalOrder.length === 0) {
      console.log('没有保存的原始顺序');
      this.showNotification('没有保存的原始顺序');
      return;
    }

    this.originalOrder.forEach(item => {
      if (item.nextSibling) {
        item.parent.insertBefore(item.element, item.nextSibling);
      } else {
        item.parent.appendChild(item.element);
      }
    });

    this.showNotification('已恢复原始顺序');
    console.log('恢复原始顺序完成');
  }
}

// 初始化排序器
const tikTokSorter = new TikTokSorter();
