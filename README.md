# TikTok搜索结果排序器

一个Chrome扩展，用于对TikTok搜索结果进行排序。

## 功能特性

- 📈 按点赞量排序（高到低/低到高）
- 🕐 按发布时间排序（最新/最旧）
- 🔄 恢复原始顺序
- 🎨 友好的用户界面
- ⚡ 实时排序，无需刷新页面

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装完成！

## 使用方法

1. 在TikTok上进行搜索 (例如: https://www.tiktok.com/search?q=asmr)
2. 点击浏览器工具栏中的扩展图标
3. 选择你想要的排序方式：
   - 按点赞量排序（高到低）
   - 按点赞量排序（低到高）
   - 按时间排序（最新）
   - 按时间排序（最旧）
4. 点击"应用排序"
5. 如需恢复原始顺序，点击"恢复原始顺序"

## 技术实现

- **Manifest V3**: 使用最新的Chrome扩展API
- **Content Script**: 注入到TikTok页面进行DOM操作
- **智能选择器**: 自动识别TikTok的视频容器
- **数据解析**: 智能解析点赞数和发布时间
- **动画效果**: 平滑的排序动画

## 文件结构

```
TikTokExt/
├── manifest.json      # 扩展配置文件
├── popup.html         # 弹出窗口界面
├── popup.js          # 弹出窗口逻辑
├── content.js        # 内容脚本（主要排序逻辑）
├── styles.css        # 样式文件
├── icons/            # 图标文件夹
└── README.md         # 说明文档
```

## 注意事项

- 此扩展仅在TikTok搜索页面工作
- 排序基于页面当前加载的视频，不会加载更多视频
- 如果TikTok更新了页面结构，可能需要更新选择器

## 开发说明

如果你想修改或改进此扩展：

1. 修改 `content.js` 中的选择器以适应TikTok的DOM结构变化
2. 在 `popup.html` 中添加新的排序选项
3. 更新 `styles.css` 来改善视觉效果

## 许可证

MIT License
