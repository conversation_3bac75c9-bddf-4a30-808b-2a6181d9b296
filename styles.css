/* TikTok搜索结果排序器样式 */

/* 为排序后的视频添加动画效果 */
[data-e2e="search_top-item"],
[data-e2e="search-card-item"],
div[data-e2e*="search"],
.tiktok-search-item,
div[class*="DivItemContainer"],
div[class*="ItemContainer"] {
  transition: all 0.3s ease-in-out;
}

/* 排序指示器 */
.tiktok-sorter-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #ff0050;
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  z-index: 10000;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-20px); }
  20% { opacity: 1; transform: translateY(0); }
  80% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-20px); }
}

/* 高亮排序相关的元素 */
.tiktok-sorter-highlight {
  outline: 2px solid #ff0050 !important;
  outline-offset: 2px;
  border-radius: 4px;
}
