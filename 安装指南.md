# TikTok搜索结果排序器 - 安装指南

## 📦 安装步骤

### 1. 准备文件
确保你有以下文件：
- `manifest.json` - 扩展配置文件
- `popup.html` - 弹出窗口界面
- `popup.js` - 弹出窗口逻辑
- `content.js` - 内容脚本
- `styles.css` - 样式文件
- `icons/` 文件夹（可选，用于图标）

### 2. 打开Chrome扩展管理页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 按回车键进入扩展管理页面

### 3. 启用开发者模式
1. 在扩展管理页面的右上角找到"开发者模式"开关
2. 点击开关，启用开发者模式
3. 页面会显示更多选项

### 4. 加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 在文件选择器中，导航到包含扩展文件的文件夹
3. 选择包含 `manifest.json` 的文件夹
4. 点击"选择文件夹"

### 5. 验证安装
1. 扩展应该出现在扩展列表中
2. 扩展图标应该出现在浏览器工具栏中
3. 如果有错误，会在扩展卡片中显示

## 🚀 使用方法

### 1. 访问TikTok搜索页面
打开 TikTok 并进行搜索，例如：
```
https://www.tiktok.com/search?q=asmr%20ocean
```

### 2. 使用排序功能
1. 点击浏览器工具栏中的扩展图标
2. 在弹出窗口中选择排序方式：
   - 📈 按点赞量排序 (高到低)
   - 📉 按点赞量排序 (低到高)
   - 🕐 按时间排序 (最新)
   - 🕑 按时间排序 (最旧)
3. 点击"应用排序"按钮
4. 页面会自动重新排列搜索结果

### 3. 恢复原始顺序
如果想要恢复TikTok的原始排序：
1. 再次点击扩展图标
2. 点击"恢复原始顺序"按钮

## 🔧 故障排除

### 扩展无法加载
- 检查 `manifest.json` 文件格式是否正确
- 确保所有必需的文件都在同一个文件夹中
- 查看扩展管理页面中的错误信息

### 排序功能不工作
- 确保你在TikTok搜索页面（URL包含 `/search`）
- 刷新页面后重试
- 检查浏览器控制台是否有错误信息

### 找不到视频内容
- TikTok可能更新了页面结构
- 等待页面完全加载后再尝试排序
- 确保搜索结果已经显示

## 📝 注意事项

1. **仅在搜索页面工作**：此扩展只在TikTok搜索结果页面有效
2. **当前页面内容**：只对当前加载的视频进行排序，不会加载更多内容
3. **页面刷新**：刷新页面后会恢复原始顺序
4. **兼容性**：如果TikTok更新页面结构，可能需要更新扩展

## 🛠️ 开发者信息

如果你想修改扩展：
1. 编辑相应的文件
2. 在扩展管理页面点击"重新加载"按钮
3. 测试修改后的功能

主要文件说明：
- `content.js`：包含主要的排序逻辑
- `popup.html/js`：用户界面和交互
- `manifest.json`：扩展权限和配置
