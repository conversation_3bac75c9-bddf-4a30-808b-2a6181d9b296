document.addEventListener('DOMContentLoaded', function() {
  const sortOptions = document.querySelectorAll('.sort-option');
  const applySortBtn = document.getElementById('applySortBtn');
  const resetBtn = document.getElementById('resetBtn');
  
  let selectedSort = null;
  
  // 处理排序选项点击
  sortOptions.forEach(option => {
    option.addEventListener('click', function() {
      // 移除其他选项的active类
      sortOptions.forEach(opt => opt.classList.remove('active'));
      // 添加active类到当前选项
      this.classList.add('active');
      selectedSort = this.dataset.sort;
    });
  });
  
  // 应用排序
  applySortBtn.addEventListener('click', function() {
    if (!selectedSort) {
      alert('请先选择一个排序方式');
      return;
    }
    
    // 向content script发送消息
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'sort',
        sortType: selectedSort
      }, function(response) {
        if (response && response.success) {
          console.log('排序成功');
        } else {
          console.log('排序失败或没有找到搜索结果');
        }
      });
    });
    
    // 关闭popup
    window.close();
  });
  
  // 重置排序
  resetBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'reset'
      }, function(response) {
        if (response && response.success) {
          console.log('重置成功');
        }
      });
    });
    
    // 关闭popup
    window.close();
  });
});
